#!/usr/bin/env python3
"""
Test script to verify tenant-aware logging is working correctly.
"""

import sys
import logging
import os

# Add project root to Python path
sys.path.append(".")

from config.config import TenantAwareFormatter


def test_tenant_logging():
    """Test tenant-aware logging functionality"""
    
    # Create a test logger
    logger = logging.getLogger("test_logger")
    logger.setLevel(logging.INFO)
    
    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Test different tenant formatters
    tenants = ["CUSTOMER", "INDX", "RISHUX", "GLOBAL"]
    
    for tenant in tenants:
        print(f"\n=== Testing logging for tenant: {tenant} ===")
        
        # Create tenant-aware formatter
        formatter = TenantAwareFormatter(
            '%(asctime)s - %(name)s - %(tenant_name)s - %(levelname)s - %(message)s', 
            tenant
        )
        console_handler.setFormatter(formatter)
        
        # Clear and add handler
        logger.handlers.clear()
        logger.addHandler(console_handler)
        
        # Test different log levels
        logger.info(f"Finished processing for user 635834411")
        logger.warning(f"Warning message for tenant {tenant}")
        logger.error(f"Error message for tenant {tenant}")
        
        # Test with different user IDs
        for user_id in [123456789, 987654321]:
            logger.info(f"Processing request for user {user_id}")
            logger.info(f"User {user_id} subscription verified")


def test_config_logging():
    """Test Config class tenant-aware logging"""
    print("\n=== Testing Config class tenant-aware logging ===")
    
    try:
        # Test creating configs for different tenants
        from config.config import Config
        
        # Test with a mock tenant name
        test_tenant = "TEST_TENANT"
        print(f"Creating config for tenant: {test_tenant}")
        
        # This would normally connect to MongoDB, but we'll just test the logging setup
        # config = Config(tenant_name=test_tenant)
        print("Config tenant-aware logging setup would work here")
        
    except Exception as e:
        print(f"Expected error (no MongoDB connection): {e}")


if __name__ == "__main__":
    print("Testing Tenant-Aware Logging Implementation")
    print("=" * 50)
    
    test_tenant_logging()
    test_config_logging()
    
    print("\n" + "=" * 50)
    print("Tenant-aware logging test completed!")
    print("\nExpected log format:")
    print("YYYY-MM-DD HH:MM:SS,mmm - logger_name - TENANT_NAME - LEVEL - message")
