from db.database import Database
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.ext import ContextTypes, ConversationHandler

# Define conversation states
WAITING_FOR_EMAIL = 1
WAITING_FOR_WHATSAPP = 2
WAITING_FOR_EXPERIENCE = 3

class SubscriptionHandler:
    def __init__(self):
        self.db = Database()
    
    def check_subscription(self, user_id):
        """Check if a user has an active subscription"""
        return self.db.check_subscription(user_id)
    
    def add_subscription(self, user_id, access_code):
        """Add a new subscription for a user"""
        # Verify the access code first
        if self.db.verify_access_code(access_code):
            self.db.add_subscription(user_id, access_code)
            return True
        return False
        
    async def start_subscription_flow(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start the information collection flow"""
        user = update.message.from_user
        context.user_data['username'] = user.username or f"{user.first_name} {user.last_name}"
        context.user_data['user_id'] = user.id
        
        await update.message.reply_text(
            "Please enter your email address:",
            reply_markup=ForceReply(selective=True)
        )
        return WAITING_FOR_EMAIL

    async def collect_email(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Collect user email"""
        context.user_data['email'] = update.message.text
        
        await update.message.reply_text(
            "Please enter your WhatsApp number:",
            reply_markup=ForceReply(selective=True)
        )
        return WAITING_FOR_WHATSAPP

    async def collect_whatsapp(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Collect WhatsApp number"""
        context.user_data['whatsapp'] = update.message.text
        
        keyboard = [
            [InlineKeyboardButton("Beginner", callback_data="beginner")],
            [InlineKeyboardButton("Intermediate", callback_data="intermediate")],
            [InlineKeyboardButton("Pro", callback_data="pro")]
        ]
        
        await update.message.reply_text(
            "Select your trading experience level:",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
        return WAITING_FOR_EXPERIENCE

    async def complete_subscription(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Finalize subscription with all collected data"""
        query = update.callback_query
        await query.answer()
        
        context.user_data['trading_experience'] = query.data
        
        # Store all collected data
        success = self.db.add_subscription(
            user_id=context.user_data['user_id'],
            access_code=context.user_data['access_code']
        )
        
        if success:
            await query.edit_message_text("✅ Registration complete! Thank you for your information.")
        else:
            await query.edit_message_text("❌ Error saving your information. Please try again.")
        
        return ConversationHandler.END
        
    async def cancel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Cancel the conversation."""
        await update.message.reply_text("Operation cancelled.")
        return ConversationHandler.END