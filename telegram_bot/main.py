import sys
import logging
from src.multi_tenant_bot import MultiTenantBotManager

class TenantAwareFormatter(logging.Formatter):
    """Custom formatter that includes tenant name in log messages"""

    def __init__(self, fmt=None, tenant_name="GLOBAL"):
        super().__init__(fmt)
        self.tenant_name = tenant_name

    def format(self, record):
        # Add tenant_name to the log record
        record.tenant_name = self.tenant_name
        return super().format(record)


def setup_logging():
    """Set up basic tenant-aware logging configuration"""
    # Create root logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Create file handler
    file_handler = logging.FileHandler('bot.log')
    file_handler.setLevel(logging.INFO)

    # Create tenant-aware formatter for global operations
    formatter = TenantAwareFormatter('%(asctime)s - %(name)s - %(tenant_name)s - %(levelname)s - %(message)s', "GLOBAL")
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    return logging.getLogger(__name__)

def main():
    """Main entry point for the application - always runs in multi-tenant mode"""
    logger = setup_logging()
    logger.info("Starting Telegram Bot application in multi-tenant mode")

    # Initialize multi-tenant bot manager
    logger.info("Initializing Multi-Tenant Bot Manager")
    manager = MultiTenantBotManager()

    # Initialize bots for all tenants
    logger.info("Initializing bots for all tenants")
    manager.initialize_bots()

    # Start all bots
    logger.info("Starting all bots")
    manager.start_all_bots()

    # Keep the main thread alive
    try:
        logger.info("All bots started. Press Ctrl+C to exit.")
        # This will keep the main thread alive until interrupted
        import time
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        logger.info("Received interrupt signal. Shutting down...")
        manager.stop_all_bots()
        logger.info("Shutdown complete")

if __name__ == "__main__":
    main()
