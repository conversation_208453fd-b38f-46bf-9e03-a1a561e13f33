import sys
import logging
from src.multi_tenant_bot import MultiTenantBotManager

def setup_logging():
    """Set up basic logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('bot.log')
        ]
    )
    return logging.getLogger(__name__)

def main():
    """Main entry point for the application - always runs in multi-tenant mode"""
    logger = setup_logging()
    logger.info("Starting Telegram Bot application in multi-tenant mode")

    # Initialize multi-tenant bot manager
    logger.info("Initializing Multi-Tenant Bot Manager")
    manager = MultiTenantBotManager()

    # Initialize bots for all tenants
    logger.info("Initializing bots for all tenants")
    manager.initialize_bots()

    # Start all bots
    logger.info("Starting all bots")
    manager.start_all_bots()

    # Keep the main thread alive
    try:
        logger.info("All bots started. Press Ctrl+C to exit.")
        # This will keep the main thread alive until interrupted
        import time
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        logger.info("Received interrupt signal. Shutting down...")
        manager.stop_all_bots()
        logger.info("Shutdown complete")

if __name__ == "__main__":
    main()
