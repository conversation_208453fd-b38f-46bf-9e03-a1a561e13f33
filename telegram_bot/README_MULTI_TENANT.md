# Multi-Tenant Telegram Bot Architecture

This document explains how to use the multi-tenant architecture for the Telegram bot.

## Overview

The multi-tenant architecture allows running a single bot application that connects to multiple tenant databases. Each tenant has its own database with the same structure, and the bot dynamically discovers and connects to all tenant databases.

## Database Configuration

The MongoDB connection URI is read from the `config/config.json` file. Make sure this file contains the correct MongoDB connection information:

```json
{
    "database": {
        "mongo_uri": "mongodb://mongodb:27017/",
        "db_name": "admin_panel"
    }
}
```

## Database Structure

Each tenant database follows the naming convention `{tenant_name}_custdb` and contains the following collections:

- `telegram_bots`: Contains bot configuration (token, channel_id, etc.)
- `access_code`: Contains valid access codes
- `subscriptions`: Contains user subscription data
- `master_user_data`: Contains comprehensive user data

## Creating a New Tenant

To create a new tenant database, use the `tools/create_tenant.py` script:

```bash
python tools/create_tenant.py customer1 --bot-token "YOUR_BOT_TOKEN" --channel-id "YOUR_CHANNEL_ID"
```

This will create a new tenant database named `customer1_custdb` with the required collections and bot configuration. The script will automatically use the MongoDB URI from your `config/config.json` file.

## Running the Bot

The bot always runs in multi-tenant mode, automatically discovering and connecting to all tenant databases:

```bash
python main.py
```

## Testing Multi-Tenant Functionality

To test the multi-tenant functionality:

```bash
python tests/test_multi_tenant.py
```

This will discover all tenant databases, test connections, and verify bot configurations.

## How It Works

1. The `TenantDatabaseManager` discovers all tenant databases on the MongoDB server using the connection URI from `config/config.json`.
2. The `MultiTenantBotManager` creates a bot instance for each tenant.
3. Each bot instance connects to its tenant database and retrieves its configuration.
4. All bot instances run concurrently in separate threads.

## Tenant Database Requirements

Each tenant database must have:

1. A `telegram_bots` collection with at least one document containing:
   - `token`: Telegram bot token
   - `channel_id`: Telegram channel ID
   - `is_active`: Set to `true`

2. The required collections with proper indexes:
   - `access_code` with unique index on `code`
   - `subscriptions` with unique index on `user_id`
   - `master_user_data` with unique index on `user_id`

## Troubleshooting

If a tenant bot fails to start, check:

1. The tenant database exists and follows the naming convention `{tenant_name}_custdb`
2. The `telegram_bots` collection contains valid bot configuration
3. The bot token and channel ID are valid
4. The MongoDB URI in `config/config.json` is correct

Logs are written to `bot.log` and can be used to diagnose issues.
