FROM python:3.11-slim

# Install git
RUN apt-get update && apt-get install -y git && apt-get clean

WORKDIR /app

# Install dependencies
RUN pip install --upgrade pip
RUN pip install setuptools wheel

# Copy requirements file
COPY requirements.txt .

# Install packages from requirements.txt except pyTelegramBotAPI
RUN pip install python-telegram-bot==20.6 pymongo==4.5.0 tabulate==0.9.0 httpx>=0.24.0 sqlalchemy

# Create a simple pyTelegramBotAPI package
RUN mkdir -p /usr/local/lib/python3.11/site-packages/telebot
RUN touch /usr/local/lib/python3.11/site-packages/telebot/__init__.py
RUN echo 'def TeleBot(*args, **kwargs): return None' > /usr/local/lib/python3.11/site-packages/telebot/__init__.py

# Copy project files
COPY . .

# Create necessary directories
RUN mkdir -p logs data db

# Run the bot
CMD ["python", "main.py"]