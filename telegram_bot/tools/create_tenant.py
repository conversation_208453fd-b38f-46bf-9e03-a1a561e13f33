#!/usr/bin/env python3
"""
Tool for creating and initializing a new tenant database.
This script:
1. Creates a new tenant database with the required collections
2. Adds a bot configuration to the telegram_bots collection
"""

import sys
import argparse
import logging
from datetime import datetime
from pymongo import MongoClient

# Add project root to Python path
sys.path.append("/Users/<USER>/Documents/aws/project/telegram_bot")

def setup_logging():
    """Set up basic logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def create_tenant_database(mongo_uri, tenant_name, bot_token=None, channel_id=None, bot_name=None, bot_username=None):
    """
    Create and initialize a new tenant database.

    Args:
        mongo_uri: MongoDB connection URI
        tenant_name: Name of the tenant (without _custdb suffix)
        bot_token: Telegram bot token
        channel_id: Telegram channel ID
        bot_name: Name of the bot
        bot_username: Username of the bot

    Returns:
        True if successful, False otherwise
    """
    logger = setup_logging()
    logger.info(f"Creating tenant database for: {tenant_name}")

    try:
        # Connect to MongoDB
        client = MongoClient(mongo_uri)

        # Create tenant database
        db_name = f"{tenant_name}_custdb"
        db = client[db_name]

        # Create collections
        logger.info(f"Creating collections in {db_name}")

        # Create access_code collection
        if 'access_code' not in db.list_collection_names():
            db.create_collection('access_code')
            # Create unique index on code field
            db.access_code.create_index('code', unique=True)
            logger.info("Created access_code collection")

        # Create subscriptions collection
        if 'subscriptions' not in db.list_collection_names():
            db.create_collection('subscriptions')
            # Create unique index on user_id field
            db.subscriptions.create_index('user_id', unique=True)
            logger.info("Created subscriptions collection")

        # Create master_user_data collection
        if 'master_user_data' not in db.list_collection_names():
            db.create_collection('master_user_data')
            # Create unique index on user_id field
            db.master_user_data.create_index('user_id', unique=True)
            logger.info("Created master_user_data collection")

        # Create telegram_bots collection
        if 'telegram_bots' not in db.list_collection_names():
            db.create_collection('telegram_bots')
            # Create index on user_id field
            db.telegram_bots.create_index('user_id')
            logger.info("Created telegram_bots collection")

        # Add bot configuration if provided
        if bot_token and channel_id:
            # Check if bot config already exists
            existing_bot = db.telegram_bots.find_one({'is_active': True})

            if existing_bot:
                logger.warning(f"Bot configuration already exists in {db_name}")
                logger.info("Updating existing bot configuration")

                # Update existing bot
                db.telegram_bots.update_one(
                    {'_id': existing_bot['_id']},
                    {'$set': {
                        'token': bot_token,
                        'channel_id': channel_id,
                        'name': bot_name or f"{tenant_name}_bot",
                        'username': bot_username or f"@{tenant_name}_bot",
                        'tenant_name': tenant_name,
                        'is_active': True,
                        'last_updated': datetime.utcnow()
                    }}
                )
            else:
                # Add new bot configuration
                db.telegram_bots.insert_one({
                    'token': bot_token,
                    'channel_id': channel_id,
                    'name': bot_name or f"{tenant_name}_bot",
                    'username': bot_username or f"@{tenant_name}_bot",
                    'tenant_name': tenant_name,
                    'is_active': True,
                    'created_at': datetime.utcnow(),
                    'last_updated': datetime.utcnow()
                })
                logger.info("Added bot configuration")

        logger.info(f"Successfully created and initialized tenant database: {db_name}")
        return True

    except Exception as e:
        logger.error(f"Error creating tenant database: {e}")
        return False

def main():
    """Main entry point for the tool"""
    # Set up logging
    logger = setup_logging()

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Create and initialize a new tenant database')
    parser.add_argument('tenant_name', help='Name of the tenant (without _custdb suffix)')
    parser.add_argument('--mongo-uri', help='MongoDB connection URI')
    parser.add_argument('--bot-token', help='Telegram bot token')
    parser.add_argument('--channel-id', help='Telegram channel ID')
    parser.add_argument('--bot-name', help='Name of the bot')
    parser.add_argument('--bot-username', help='Username of the bot')
    args = parser.parse_args()

    # Get MongoDB URI from arguments or config.json
    if args.mongo_uri:
        mongo_uri = args.mongo_uri
    else:
        try:
            import json
            with open('./config/config.json', 'r') as f:
                config = json.load(f)
                mongo_uri = config.get('database', {}).get('mongo_uri')
                logger.info(f"Using MongoDB URI from config.json: {mongo_uri}")
        except Exception as e:
            logger.warning(f"Could not load MongoDB URI from config.json: {e}")
            mongo_uri = 'mongodb://mongodb:27017/'
            logger.info(f"Using default MongoDB URI: {mongo_uri}")

    # Create tenant database
    success = create_tenant_database(
        mongo_uri=mongo_uri,
        tenant_name=args.tenant_name,
        bot_token=args.bot_token,
        channel_id=args.channel_id,
        bot_name=args.bot_name,
        bot_username=args.bot_username
    )

    if success:
        print(f"Successfully created tenant database: {args.tenant_name}_custdb")

        if not args.bot_token or not args.channel_id:
            print("\nNOTE: Bot token and channel ID were not provided.")
            print("You will need to add them to the telegram_bots collection before the bot can run.")
            print(f"Database: {args.tenant_name}_custdb")
            print("Collection: telegram_bots")
            print("Required fields: token, channel_id, is_active=True")
    else:
        print(f"Failed to create tenant database. See log for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
