#!/usr/bin/env python3
"""
Test script for multi-tenant bot functionality.
This script demonstrates how to:
1. Initialize the multi-tenant bot manager
2. Start bots for all tenants
3. Keep the main thread alive
4. Stop all bots gracefully
"""

import sys
import time
import logging
import signal
import json

# Add project root to Python path
sys.path.append("/Users/<USER>/Documents/aws/project/telegram_bot")

from src.multi_tenant_bot import MultiTenantBotManager

def setup_logging():
    """Set up basic logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def get_mongo_uri():
    """Get MongoDB URI from config.json"""
    try:
        with open('./config/config.json', 'r') as f:
            config = json.load(f)
            mongo_uri = config.get('database', {}).get('mongo_uri')
            return mongo_uri
    except Exception as e:
        print(f"Could not load MongoDB URI from config.json: {e}")
        return 'mongodb://mongodb:27017/'

def run_multi_tenant_bot(run_time=60):
    """
    Run the multi-tenant bot for a specified time.
    
    Args:
        run_time: Number of seconds to run the bot
    """
    logger = setup_logging()
    
    # Initialize multi-tenant bot manager
    logger.info("Initializing Multi-Tenant Bot Manager")
    manager = MultiTenantBotManager()
    
    # Set up signal handler for graceful shutdown
    def signal_handler(sig, frame):
        logger.info("Received interrupt signal. Shutting down...")
        manager.stop_all_bots()
        logger.info("Shutdown complete")
        sys.exit(0)
    
    # Register signal handler
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # Initialize bots for all tenants
        logger.info("Initializing bots for all tenants")
        manager.initialize_bots()
        
        # Start all bots
        logger.info("Starting all bots")
        manager.start_all_bots()
        
        # Keep the main thread alive
        logger.info(f"All bots started. Running for {run_time} seconds. Press Ctrl+C to exit.")
        time.sleep(run_time)
        
        # Stop all bots
        logger.info("Run time completed. Shutting down...")
        manager.stop_all_bots()
        logger.info("Shutdown complete")
        
    except Exception as e:
        logger.error(f"Error running multi-tenant bot: {e}", exc_info=True)
        manager.stop_all_bots()

def main():
    """Main entry point for the test script"""
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Test multi-tenant bot functionality')
    parser.add_argument('--time', type=int, default=60, help='Number of seconds to run the bot')
    args = parser.parse_args()
    
    # Run multi-tenant bot
    run_multi_tenant_bot(args.time)

if __name__ == "__main__":
    main()
