"""
Bot helper utilities for common operations.
Centralizes bot-related functionality and data access patterns.
"""

import logging
from typing import Optional, Dict, Any
from datetime import datetime, timezone


class BotHelpers:
    """Helper utilities for bot operations"""
    
    def __init__(self, config, logger: Optional[logging.Logger] = None):
        """Initialize the bot helpers"""
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
    
    def get_broker_referral_link(self) -> str:
        """
        Get the broker referral link from MongoDB telegram_bots collection.
        
        Returns:
            Referral link URL
        """
        try:
            # Access the database through config
            if not self.config.db:
                self.logger.error("Database connection not available")
                return "https://one.exnesstrack.org/a/verifyme"

            # Query the telegram_bots collection to get bot data
            # Get the first active bot document
            bot_data = self.config.db.db.telegram_bots.find_one(
                {'is_active': True},
                {'data.referralLink': 1}  # Only fetch the referralLink field
            )

            if bot_data and 'data' in bot_data and 'referralLink' in bot_data['data']:
                referral_link = bot_data['data']['referralLink']
                self.logger.info(f"Retrieved broker referral link from MongoDB: {referral_link}")
                return referral_link
            else:
                self.logger.warning("Broker referral link not found in MongoDB bot data, using default")
                return "https://one.exnesstrack.org/a/verifyme"

        except Exception as e:
            self.logger.error(f"Error retrieving broker referral link from MongoDB: {str(e)}")
            # Fall back to default value
            return "https://one.exnesstrack.org/a/verifyme"
    
    def get_pdf_from_database(self) -> Optional[bytes]:
        """
        Get PDF file data from MongoDB telegram_bots collection.
        
        Returns:
            PDF file data as bytes or None if not found
        """
        try:
            if not self.config.db:
                self.logger.error("Database connection not available")
                return None

            # Query the telegram_bots collection to get PDF data
            bot_data = self.config.db.db.telegram_bots.find_one(
                {'is_active': True},
                {'data.pdf': 1}  # Only fetch the PDF field
            )

            if bot_data and 'data' in bot_data and 'pdf' in bot_data['data']:
                pdf_data = bot_data['data']['pdf']
                self.logger.info("Retrieved PDF data from MongoDB")
                return pdf_data
            else:
                self.logger.info("PDF data not found in MongoDB bot data")
                return None

        except Exception as e:
            self.logger.error(f"Error retrieving PDF from MongoDB: {str(e)}")
            return None
    
    def create_user_interaction_data(self, user_id: int, bot_id: Optional[int], interaction_type: str, details: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Create standardized user interaction data for logging.
        
        Args:
            user_id: Telegram user ID
            bot_id: Bot ID
            interaction_type: Type of interaction (e.g., 'button_click', 'command', 'message')
            details: Additional interaction details
            
        Returns:
            Formatted interaction data
        """
        interaction_data = {
            'user_id': user_id,
            'bot_id': bot_id,
            'interaction_type': interaction_type,
            'timestamp': datetime.now(timezone.utc),
            'tenant_name': self.config.get_tenant_name()
        }
        
        if details:
            interaction_data['details'] = details
            
        return interaction_data
    
    def log_user_interaction(self, user_id: int, bot_id: Optional[int], interaction_type: str, details: Dict[str, Any] = None) -> bool:
        """
        Log user interaction to the database.
        
        Args:
            user_id: Telegram user ID
            bot_id: Bot ID
            interaction_type: Type of interaction
            details: Additional interaction details
            
        Returns:
            True if successful, False otherwise
        """
        try:
            interaction_data = self.create_user_interaction_data(user_id, bot_id, interaction_type, details)
            
            # Here you would save to your interaction logging system
            # For now, just log it
            self.logger.info(f"User interaction logged: {interaction_data}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error logging user interaction: {str(e)}")
            return False
    
    def get_tenant_name_from_context(self, context) -> Optional[str]:
        """
        Extract tenant name from bot context.
        
        Args:
            context: Bot context object
            
        Returns:
            Tenant name or None if not found
        """
        try:
            if hasattr(context, 'bot_data') and 'tenant_name' in context.bot_data:
                return context.bot_data['tenant_name']
            elif hasattr(context, 'user_data') and 'tenant_name' in context.user_data:
                return context.user_data['tenant_name']
            else:
                return self.config.get_tenant_name()
        except Exception as e:
            self.logger.error(f"Error getting tenant name from context: {str(e)}")
            return self.config.get_tenant_name()
    
    def format_user_status_message(self, user_data: Dict[str, Any]) -> str:
        """
        Format a user status message based on user data.
        
        Args:
            user_data: User data dictionary
            
        Returns:
            Formatted status message
        """
        if not user_data:
            return "❌ User not found"
        
        status = user_data.get('user_status', 'unknown')
        subscription = user_data.get('subscription', 'unknown')
        verified = user_data.get('user_verify', False)
        
        status_emoji = {
            'active': '✅',
            'pending_verification': '⏳',
            'expired': '❌',
            'inactive': '⚪'
        }.get(status, '❓')
        
        message = f"{status_emoji} **Status:** {status.replace('_', ' ').title()}\n"
        message += f"📋 **Subscription:** {subscription.title()}\n"
        message += f"✓ **Verified:** {'Yes' if verified else 'No'}\n"
        
        if 'registration_time' in user_data:
            reg_time = user_data['registration_time']
            if isinstance(reg_time, datetime):
                message += f"📅 **Registered:** {reg_time.strftime('%Y-%m-%d %H:%M UTC')}\n"
        
        return message
    
    def validate_access_code_format(self, code: str) -> tuple[bool, str]:
        """
        Validate access code format.
        
        Args:
            code: Access code to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not code or not code.strip():
            return False, "Access code cannot be empty"
        
        code = code.strip()
        
        # Basic format validation - adjust as needed for your access code format
        if len(code) < 3:
            return False, "Access code is too short"
        
        if len(code) > 50:
            return False, "Access code is too long"
        
        # Add more specific validation rules as needed
        return True, ""
