#!/usr/bin/env python3
"""
Test script for multi-tenant functionality.
This script demonstrates how to:
1. Connect to MongoDB and list all tenant databases
2. Create a database connection for a specific tenant
3. Retrieve bot configuration from a tenant database
"""

import os
import sys
import logging

# Add project root to Python path
sys.path.append("/Users/<USER>/Documents/aws/project/telegram_bot")

from db.database import Database
from db.tenant_manager import TenantDatabaseManager
from config.config import Config

def setup_logging():
    """Set up basic logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def test_tenant_discovery():
    """Test discovering tenant databases"""
    logger = setup_logging()
    logger.info("Testing tenant discovery...")
    
    # Get MongoDB URI from environment or use default
    mongo_uri = os.environ.get('MONGO_URI') or 'mongodb://localhost:27017/'
    
    # Create tenant manager
    tenant_manager = TenantDatabaseManager(mongo_uri)
    
    # Get all tenant names
    tenant_names = tenant_manager.get_all_tenant_names()
    
    logger.info(f"Found {len(tenant_names)} tenant databases:")
    for tenant in tenant_names:
        logger.info(f"  - {tenant}")
    
    return tenant_names

def test_tenant_connection(tenant_name):
    """Test connecting to a specific tenant database"""
    logger = setup_logging()
    logger.info(f"Testing connection to tenant database: {tenant_name}")
    
    # Get MongoDB URI from environment or use default
    mongo_uri = os.environ.get('MONGO_URI') or 'mongodb://localhost:27017/'
    
    # Create database connection for this tenant
    db = Database(mongo_uri, tenant_name=tenant_name)
    
    # Test getting bot info
    bot_info = db.get_telegram_bot_info()
    
    if bot_info:
        logger.info(f"Successfully retrieved bot info for tenant {tenant_name}:")
        logger.info(f"  Bot name: {bot_info.get('name', 'Unknown')}")
        logger.info(f"  Bot username: {bot_info.get('username', 'Unknown')}")
        logger.info(f"  Channel ID: {bot_info.get('channel_id', 'Unknown')}")
    else:
        logger.warning(f"No bot info found for tenant {tenant_name}")
    
    return bot_info

def test_tenant_config(tenant_name):
    """Test loading configuration for a specific tenant"""
    logger = setup_logging()
    logger.info(f"Testing configuration for tenant: {tenant_name}")
    
    # Create config for this tenant
    config = Config(tenant_name=tenant_name)
    
    # Get bot token and channel ID
    bot_token = config.get('bot_token')
    channel_id = config.get('channel_id')
    
    logger.info(f"Bot token for tenant {tenant_name}: {bot_token[:5]}...")
    logger.info(f"Channel ID for tenant {tenant_name}: {channel_id}")
    
    return config

def main():
    """Main entry point for the test script"""
    logger = setup_logging()
    logger.info("Starting multi-tenant test script")
    
    # Test tenant discovery
    tenant_names = test_tenant_discovery()
    
    if not tenant_names:
        logger.warning("No tenant databases found. Please create at least one tenant database.")
        return
    
    # Test connection to each tenant
    for tenant_name in tenant_names:
        try:
            # Test tenant connection
            bot_info = test_tenant_connection(tenant_name)
            
            if bot_info:
                # Test tenant config
                config = test_tenant_config(tenant_name)
        except Exception as e:
            logger.error(f"Error testing tenant {tenant_name}: {e}")
    
    logger.info("Multi-tenant test script completed")

if __name__ == "__main__":
    main()
