import os
import unittest

class TestClaimMembershipImage(unittest.TestCase):
    def test_claim_membership_image_exists(self):
        """Test if the claim membership image exists"""
        # Check if the claim membership image exists
        claim_membership_image_path = os.path.join('telegram_bot/assets', 'claim_membership.webp')
        self.assertTrue(os.path.exists(claim_membership_image_path), f"Claim membership image not found at {claim_membership_image_path}")
        self.assertTrue(os.path.isfile(claim_membership_image_path), f"Path exists but is not a file: {claim_membership_image_path}")

if __name__ == '__main__':
    unittest.main()
