import os
import unittest

class TestAllImages(unittest.TestCase):
    def test_all_images_exist(self):
        """Test if all required images exist"""
        # Define the list of required images
        required_images = [
            ('welcome.webp', 'Welcome image'),
            ('membership_offer.webp', 'Membership offer image'),
            ('claim_membership.webp', 'Claim membership image'),
            ('verify_membership.webp', 'Verify membership image'),
            ('withdrawal.webp', 'Withdrawal image'),
            ('support.webp', 'Support image')
        ]

        # Check if each image exists
        for image_name, image_description in required_images:
            image_path = os.path.join('telegram_bot/assets', image_name)
            self.assertTrue(os.path.exists(image_path), f"{image_description} not found at {image_path}")
            self.assertTrue(os.path.isfile(image_path), f"Path exists but is not a file: {image_path}")

if __name__ == '__main__':
    unittest.main()
