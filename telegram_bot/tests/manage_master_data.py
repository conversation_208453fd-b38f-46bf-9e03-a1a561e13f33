#!/usr/bin/env python3
import argparse
import os
from datetime import datetime
from db.database import Database
from tabulate import tabulate

def get_database():
    """Get database connection"""
    mongo_uri = os.getenv('MONGO_URI') or 'mongodb://fluppycoin.com:27017/'
    return Database(mongo_uri)

def sync_master_data():
    """Sync data from subscriptions to master_user_data"""
    db = get_database()
    success, message = db.sync_master_user_data()
    print(message)
    return success

def show_master_data():
    """Display all master user data"""
    db = get_database()
    users = db.get_all_master_users()
    
    if not users:
        print("No users found in master_user_data collection")
        return
    
    # Get all possible keys from all users
    all_keys = set()
    for user in users:
        all_keys.update(user.keys())
    
    # Remove _id from display
    if '_id' in all_keys:
        all_keys.remove('_id')
    
    # Sort keys for consistent display
    keys = sorted(list(all_keys))
    
    # Create rows with all fields
    rows = []
    for user in users:
        row = [user.get(key, '') for key in keys]
        rows.append(row)
    
    print(f"\nMaster User Data ({len(users)} users):")
    print(tabulate(rows, headers=keys, tablefmt='psql'))

def export_to_csv(file_path):
    """Export master user data to CSV"""
    db = get_database()
    success = db.export_users_to_csv(file_path)
    
    if success:
        print(f"Successfully exported user data to {file_path}")
    else:
        print(f"Failed to export user data to {file_path}")
    
    return success

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Master User Data Management Utility')
    parser.add_argument('--sync', action='store_true', help='Sync data from subscriptions to master_user_data')
    parser.add_argument('--show', action='store_true', help='Show all master user data')
    parser.add_argument('--export', metavar='FILE_PATH', help='Export master user data to CSV file')
    
    args = parser.parse_args()
    
    if args.sync:
        sync_master_data()
    elif args.show:
        show_master_data()
    elif args.export:
        export_to_csv(args.export)
    else:
        parser.print_help()
