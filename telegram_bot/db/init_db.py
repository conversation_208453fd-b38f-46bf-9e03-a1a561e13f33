import sys
import os

# Add project root to Python path
sys.path.append("/Users/<USER>/Documents/aws/project/telegram_bot")

from db.database import Database
from db.models import AccessCode

def init_database():
    # Initialize database (this will create tables)
    db = Database()
    
    # Add some test access codes
    with db.get_session() as session:
        # Add test access codes
        test_codes = ['123456789', 'test123', 'demo456']
        for code in test_codes:
            access_code = AccessCode(code=code)
            session.merge(access_code)
        
        print("Database initialized and test access codes added.")

if __name__ == "__main__":
    init_database()