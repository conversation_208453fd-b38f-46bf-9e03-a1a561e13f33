"""
Multi-tenant database manager for MongoDB.
Handles connections to multiple tenant databases.
"""

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
import time
import logging

class TenantDatabaseManager:
    """
    Manages connections to multiple tenant databases.

    This class handles:
    - Discovering tenant databases on the MongoDB server
    - Creating and caching connections to tenant databases
    - Providing access to tenant-specific database instances
    """

    def __init__(self, mongo_uri=None):
        """
        Initialize the tenant database manager.

        Args:
            mongo_uri: MongoDB connection URI
        """
        self.logger = logging.getLogger(__name__)

        # Use MongoDB URI from config.json if not provided
        if mongo_uri is None:
            try:
                with open('./config/config.json', 'r') as f:
                    import json
                    config = json.load(f)
                    mongo_uri = config.get('database', {}).get('mongo_uri')
                    self.logger.info(f"Using MongoDB URI from config.json: {mongo_uri}")
            except Exception as e:
                self.logger.warning(f"Could not load MongoDB URI from config.json: {e}")
                mongo_uri = 'mongodb://mongodb:27017/'
                self.logger.info(f"Using default MongoDB URI: {mongo_uri}")

        self.mongo_uri = mongo_uri
        self.client = None
        self.tenant_dbs = {}  # Cache for tenant database connections
        self.tenant_names = []  # List of discovered tenant names

        # Connect to MongoDB
        self._connect_to_mongodb()

        # Discover tenant databases
        self._discover_tenant_databases()

    def _connect_to_mongodb(self):
        """Connect to MongoDB server with retry logic"""
        max_retries = 5
        for attempt in range(max_retries):
            try:
                self.client = MongoClient(self.mongo_uri, serverSelectionTimeoutMS=5000)
                # Test the connection
                self.client.admin.command('ping')
                self.logger.info("Successfully connected to MongoDB server")
                return
            except (ConnectionFailure, ServerSelectionTimeoutError) as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"Failed to connect to MongoDB (attempt {attempt+1}/{max_retries}): {e}")
                    time.sleep(2)  # Wait before retrying
                else:
                    self.logger.error(f"Could not connect to MongoDB after {max_retries} attempts: {e}")
                    raise

    def _discover_tenant_databases(self):
        """
        Discover all tenant databases on the MongoDB server.
        Tenant databases are identified by the suffix '_custdb'.
        """
        if self.client is None:
            self.logger.error("Cannot discover tenant databases: MongoDB client not initialized")
            return

        try:
            # Get list of all databases
            all_dbs = self.client.list_database_names()

            # Filter for tenant databases (ending with _custdb)
            tenant_dbs = [db for db in all_dbs if db.endswith('_custdb')]

            # Extract tenant names (remove _custdb suffix)
            self.tenant_names = [db[:-7] for db in tenant_dbs]  # Remove '_custdb' (7 characters)

            self.logger.info(f"Discovered {len(self.tenant_names)} tenant databases: {', '.join(self.tenant_names)}")

            # Initialize connections to all tenant databases
            for tenant in self.tenant_names:
                self.get_tenant_db(tenant)

        except Exception as e:
            self.logger.error(f"Error discovering tenant databases: {e}")
            raise

    def get_tenant_db(self, tenant_name):
        """
        Get a database connection for a specific tenant.

        Args:
            tenant_name: Name of the tenant (without _custdb suffix)

        Returns:
            MongoDB database instance for the tenant
        """
        if tenant_name is None or tenant_name == "":
            self.logger.error("Cannot get tenant database: No tenant name provided")
            return None

        # Check if we already have a connection for this tenant
        if tenant_name in self.tenant_dbs:
            return self.tenant_dbs[tenant_name]

        # Create a new connection
        try:
            db_name = f"{tenant_name}_custdb"
            db = self.client[db_name]

            # Cache the connection
            self.tenant_dbs[tenant_name] = db

            self.logger.info(f"Created connection to tenant database: {db_name}")
            return db
        except Exception as e:
            self.logger.error(f"Error connecting to tenant database {tenant_name}_custdb: {e}")
            return None

    def get_all_tenant_names(self):
        """
        Get a list of all tenant names.

        Returns:
            List of tenant names (without _custdb suffix)
        """
        return self.tenant_names

    def refresh_tenant_list(self):
        """
        Refresh the list of tenant databases.
        Call this method if new tenants have been added to the MongoDB server.
        """
        self._discover_tenant_databases()
        return self.tenant_names
