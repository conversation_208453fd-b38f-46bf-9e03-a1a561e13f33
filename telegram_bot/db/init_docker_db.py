import sqlite3
import os

def init_database():
    # Database path in Docker environment
    db_path = '/app/db/admin_panels.db'
    
    # Ensure directory exists
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # Connect to SQLite database (creates if not exists)
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Create access_code table with auto-incrementing ID
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS access_code (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT
        )
    ''')

    # Create subscriptions table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS subscriptions (
            user_id INTEGER PRIMARY KEY,
            access_code TEXT,
            subscription_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT
        )
    ''')

    # Add some test access codes
    test_codes = ['123456789']
    for code in test_codes:
        try:
            cursor.execute('INSERT INTO access_code (code) VALUES (?)', (code,))
        except sqlite3.IntegrityError:
            print(f"Code {code} already exists")

    # Commit changes and close connection
    conn.commit()
    conn.close()
    print("Docker database initialized successfully!")

if __name__ == "__main__":
    init_database()