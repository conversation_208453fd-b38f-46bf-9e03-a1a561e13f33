import json
import os
import sys

# Add project root to Python path
sys.path.append("/Users/<USER>/Documents/aws/project/telegram_bot")

from db.database import Database

def migrate_subscriptions():
    # Path to the old JSON file
    json_path = "/Users/<USER>/Documents/aws/project/telegram_bot/data/subscriptions.json"

    # Initialize database
    db = Database()

    # Check if JSON file exists
    if os.path.exists(json_path):
        with open(json_path, 'r') as f:
            data = json.load(f)

        # Migrate users
        if 'users' in data:
            for user_id, user_data in data['users'].items():
                if user_data.get('active', False):
                    access_code = str(user_data.get('access_code', ''))

                    # Add access code if it doesn't exist
                    # Note: We don't need to check if it's used by another user during migration
                    if not db.verify_access_code(access_code):
                        with db.get_session() as session:
                            from db.models import AccessCode
                            session.merge(AccessCode(code=access_code))

                    # Add subscription
                    db.add_subscription(int(user_id), access_code)

            print(f"Migrated {len(data['users'])} users to the database")

        # Rename the old file as backup
        os.rename(json_path, f"{json_path}.bak")
        print(f"Renamed {json_path} to {json_path}.bak")

if __name__ == "__main__":
    migrate_subscriptions()