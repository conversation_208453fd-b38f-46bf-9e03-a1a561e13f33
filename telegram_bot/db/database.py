from datetime import datetime
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
import time
import logging
from db.master_user_data import MasterUserData
from db.tenant_manager import TenantDatabaseManager

class Database:
    """
    Database class for MongoDB operations.
    Supports both single-tenant and multi-tenant modes.
    """

    # Class-level tenant manager for shared access across instances
    _tenant_manager = None

    @classmethod
    def get_tenant_manager(cls, mongo_uri=None):
        """Get or create the shared tenant manager instance"""
        if cls._tenant_manager is None:
            cls._tenant_manager = TenantDatabaseManager(mongo_uri)
        return cls._tenant_manager

    def __init__(self, mongo_uri=None, tenant_name=None):
        """
        Initialize database connection.

        Args:
            mongo_uri: MongoDB connection URI
            tenant_name: Optional tenant name for multi-tenant mode
        """
        self.logger = logging.getLogger(__name__)

        # Use MongoDB URI from config.json if not provided
        if mongo_uri is None:
            try:
                import json
                with open('./config/config.json', 'r') as f:
                    config = json.load(f)
                    mongo_uri = config.get('database', {}).get('mongo_uri')
                    self.logger.info(f"Using MongoDB URI from config.json: {mongo_uri}")
            except Exception as e:
                self.logger.warning(f"Could not load MongoDB URI from config.json: {e}")
                mongo_uri = 'mongodb://mongodb:27017/'
                self.logger.info(f"Using default MongoDB URI: {mongo_uri}")

        self.mongo_uri = mongo_uri
        self.tenant_name = tenant_name

        # Multi-tenant mode
        if tenant_name:
            self.logger.info(f"Initializing database in multi-tenant mode for tenant: {tenant_name}")
            # Get or create tenant manager
            tenant_manager = self.get_tenant_manager(mongo_uri)
            # Get database for this tenant
            self.db = tenant_manager.get_tenant_db(tenant_name)
            if self.db is None:
                raise ValueError(f"Could not connect to tenant database for {tenant_name}")
            self.client = tenant_manager.client  # Share the client connection

        # Single-tenant mode (backward compatibility)
        else:
            self.logger.info("Initializing database in single-tenant mode")
            self.db_name = 'admin_panel'  # Changed from 'telegram_bot' to 'admin_panel'

            # Connect to MongoDB with retry logic
            max_retries = 5
            for attempt in range(max_retries):
                try:
                    self.client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
                    # Test the connection
                    self.client.admin.command('ping')
                    self.db = self.client[self.db_name]
                    break
                except (ConnectionFailure, ServerSelectionTimeoutError) as e:
                    if attempt < max_retries - 1:
                        self.logger.warning(f"Failed to connect to MongoDB (attempt {attempt+1}/{max_retries}): {e}")
                        time.sleep(2)  # Wait before retrying
                    else:
                        self.logger.error(f"Could not connect to MongoDB: {e}")
                        raise

        # Ensure collections and indexes exist
        self._create_collections()

        # Initialize master user data manager
        self.master_user_data = MasterUserData(self.db)

    def _create_collections(self):
        """Create necessary collections and indexes"""
        # Create access_code collection if it doesn't exist
        if 'access_code' not in self.db.list_collection_names():
            self.db.create_collection('access_code')
            # Create unique index on code field
            self.db.access_code.create_index('code', unique=True)

        # Create subscriptions collection if it doesn't exist
        if 'subscriptions' not in self.db.list_collection_names():
            self.db.create_collection('subscriptions')
            # Create unique index on user_id field
            self.db.subscriptions.create_index('user_id', unique=True)

        # Create telegram_bots collection if it doesn't exist
        if 'telegram_bots' not in self.db.list_collection_names():
            self.db.create_collection('telegram_bots')
            # Create index on user_id field
            self.db.telegram_bots.create_index('user_id')

    def verify_access_code(self, code):
        """Check if an access code exists"""
        return self.db.access_code.find_one({'code': str(code)}) is not None

    def add_subscription(self, user_id, access_code, subscription_data=None):
        """Add subscription with all user details"""
        try:
            if subscription_data is None:
                subscription_data = {
                    'user_id': user_id,
                    'access_code': access_code,
                    'subscription_time': datetime.utcnow(),
                    'status': 'active'
                }

            # Force-set these fields to ensure consistency
            subscription_data.update({
                'user_id': user_id,
                'access_code': access_code,
                'status': 'active'
            })


            # Clear any legacy fields
            for legacy_field in ['username', 'email', 'whatsapp', 'trading_experience']:
                if legacy_field in subscription_data:
                    del subscription_data[legacy_field]

            # Update subscriptions collection
            self.db.subscriptions.update_one(
                {'user_id': user_id},
                {'$set': subscription_data},
                upsert=True
            )

            # Also update master user data collection
            if 'user_details' in subscription_data:
                # Create flat user data structure for master collection
                user_details = subscription_data.get('user_details', {})
                master_data = {
                    'user_id': user_id,
                    'access_code': access_code,
                    'subscription_time': subscription_data.get('subscription_time', datetime.utcnow()),
                    'status': subscription_data.get('status', 'active'),
                    'name': user_details.get('name', ''),
                    'email': user_details.get('email', ''),
                    'whatsapp': user_details.get('whatsapp', ''),
                    'trading_experience': user_details.get('trading_experience', ''),
                    'last_updated': datetime.utcnow()
                }
                self.master_user_data.add_or_update_user(user_id, master_data)

            return True
        except Exception as e:
            print(f"Error adding subscription: {e}")
            return False

    def check_subscription(self, user_id):
        """Check if a user has an active subscription"""
        return self.db.subscriptions.find_one({
            'user_id': user_id,
            'status': 'active'
        }) is not None

    def get_access_codes(self):
        """Get all access codes"""
        codes = self.db.access_code.find({})
        return [doc['code'] for doc in codes]

    def get_user_data(self, user_id):
        """Get subscription data for a user"""
        subscription = self.db.subscriptions.find_one({'user_id': user_id, 'status': 'active'})
        if subscription:
            # Convert ObjectId to string for serialization
            subscription['_id'] = str(subscription['_id'])

            # Extract user details if available in the new format
            user_details = {}
            if 'user_details' in subscription:
                user_details = subscription['user_details']
            elif 'username' in subscription and isinstance(subscription['username'], dict) and 'user_details' in subscription['username']:
                # Handle nested structure if it exists
                user_details = subscription['username']['user_details']
            return {
                'user_id': subscription['user_id'],
                'access_code': subscription['access_code'],
                'subscription_time': subscription['subscription_time'],
                'status': subscription['status'],
                'active': subscription['status'] == 'active',
                'user_details': user_details
            }
        return None

    def save_telegram_bot_data(self, user_id, data):
        """Save data to telegram_bots collection"""
        try:
            # Ensure user_id is included in the data
            data['user_id'] = user_id
            data['timestamp'] = datetime.utcnow()

            self.db.telegram_bots.update_one(
                {'user_id': user_id},
                {'$set': data},
                upsert=True
            )
            return True, "Data saved successfully"
        except Exception as e:
            error_msg = f"Error saving telegram bot data: {e}"
            print(error_msg)
            return False, error_msg

    def get_telegram_bot_info(self, bot_name=None, bot_username=None):
        """Get telegram bot information from telegram_bots collection

        Args:
            bot_name: The name of the bot to retrieve
            bot_username: The username of the bot to retrieve

        Returns:
            Dictionary containing bot information or None if not found
        """
        try:
            query = {}
            if bot_name:
                query['name'] = bot_name
            if bot_username:
                query['username'] = bot_username

            # If no filters provided, get the first active bot
            if not query:
                query['is_active'] = True

            bot_info = self.db.telegram_bots.find_one(query)

            if bot_info and '_id' in bot_info:
                bot_info['_id'] = str(bot_info['_id'])

            return bot_info
        except Exception as e:
            print(f"Error getting telegram bot info: {e}")
            return None

    # Master User Data methods
    def sync_master_user_data(self):
        """Sync all subscription data to master_user_data collection"""
        try:
            count = self.master_user_data.sync_from_subscriptions()
            return True, f"Synced {count} user records to master_user_data"
        except Exception as e:
            error_msg = f"Error syncing master user data: {e}"
            print(error_msg)
            return False, error_msg

    def get_all_master_users(self):
        """Get all users from master_user_data collection"""
        try:
            return self.master_user_data.get_all_users()
        except Exception as e:
            print(f"Error getting master users: {e}")
            return []

    def get_master_user(self, user_id):
        """Get a specific user from master_user_data collection"""
        try:
            return self.master_user_data.get_user(user_id)
        except Exception as e:
            print(f"Error getting master user {user_id}: {e}")
            return None

    def export_users_to_csv(self, file_path=None):
        """Export all user data to CSV format"""
        try:
            return self.master_user_data.export_to_csv(file_path)
        except Exception as e:
            self.logger.error(f"Error exporting users to CSV: {e}")
            return False if file_path else ""

    @classmethod
    def get_tenant_database(cls, tenant_name, mongo_uri=None):
        """
        Get a database instance for a specific tenant.

        Args:
            tenant_name: Name of the tenant
            mongo_uri: Optional MongoDB URI

        Returns:
            Database instance for the specified tenant
        """
        return Database(mongo_uri=mongo_uri, tenant_name=tenant_name)

    @classmethod
    def get_all_tenant_names(cls, mongo_uri=None):
        """
        Get a list of all tenant names.

        Args:
            mongo_uri: Optional MongoDB URI

        Returns:
            List of tenant names
        """
        tenant_manager = cls.get_tenant_manager(mongo_uri)
        return tenant_manager.get_all_tenant_names()

    def get_tenant_name(self):
        """
        Get the name of the current tenant.

        Returns:
            Tenant name or None if in single-tenant mode
        """
        return self.tenant_name